{"version": 3, "names": ["_classStaticPrivateMethodSet", "TypeError"], "sources": ["../../src/helpers/classStaticPrivateMethodSet.js"], "sourcesContent": ["/* @minVersion 7.3.2 */\n/* @onlyBabel7 */\n\nexport default function _classStaticPrivateMethodSet() {\n  throw new TypeError(\"attempted to set read only static private field\");\n}\n"], "mappings": ";;;;;;AAGe,SAASA,4BAA4BA,CAAA,EAAG;EACrD,MAAM,IAAIC,SAAS,CAAC,iDAAiD,CAAC;AACxE", "ignoreList": []}